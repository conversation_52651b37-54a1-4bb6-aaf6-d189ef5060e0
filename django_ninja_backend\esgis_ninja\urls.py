"""
URL configuration for ESGIS Ninja project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include, re_path
from ninja import NinjaAPI
from django.conf import settings
from django.http import HttpResponse
import os

# Import API routers from different apps
from api_ninja.api import api as api_router
from riskradar_ninja.api import api as riskradar_router
from green_ninja.api import api as green_router
from finance_ninja.api import api as finance_router

# Create main API instance
api = NinjaAPI(
    title="ESGIS API",
    version="1.0.0",
    description="ESGIS Django Ninja API",
    docs_url="/api/docs/",
)

# Add routers from different apps
api.add_router("/api/", api_router)
api.add_router("/api/riskradar/", riskradar_router)
api.add_router("/api/green/", green_router)
api.add_router("/api/finance/", finance_router)

def static_serve(request, path):
    """Serve static files for frontend"""
    from django.contrib.staticfiles.views import serve
    return serve(request, path)

urlpatterns = [
    path("admin/", admin.site.urls),
    path("advanced_filters/", include("advanced_filters.urls")),
    path("", api.urls),
    path("favicon.ico/", static_serve, kwargs={"path": "/static/favicon.ico"}),
    path("agrigis.svg/", static_serve, kwargs={"path": "/static/agrigis.svg"}),
    path("openshift.svg", static_serve, kwargs={"path": "/static/openshift.svg"}),
    re_path(r"^.*$", static_serve, kwargs={"path": "/static/"}),
]

# Add debug toolbar and silk URLs in development
if settings.DEBUG:
    try:
        import debug_toolbar
        urlpatterns = [path("__debug__/", include(debug_toolbar.urls)), *urlpatterns]
    except ImportError:
        pass
    
    try:
        urlpatterns = [path("silk/", include("silk.urls", namespace="silk")), *urlpatterns]
    except ImportError:
        pass
