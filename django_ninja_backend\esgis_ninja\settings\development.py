from .base import *
import os

# Development-specific settings
DEBUG = True

# Database configuration for development
DB_NAMES = os.environ.get("DB_NAMES", "agrigis")
DEFAULT_DB = os.environ.get("DEFAULT_DB", "agrigis")
DB_USERNAME = os.environ.get("DB_USERNAME", "django")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
POSTGRESQL_SERVICE_HOST = os.environ.get("POSTGRESQL_SERVICE_HOST", "localhost")
POSTGRESQL_SERVICE_PORT = int(os.environ.get("POSTGRESQL_SERVICE_PORT", 5432))

DATABASES = {}

# Using multiple names is purely to be able to use the --database flag on django commands
# Development databases can still be switched with the DEFAULT_DB variable
# e.g. DEFAULT_DB=explorer ./manage.py runserver
for name in DB_NAMES.split(", "):
    DATABASES[name] = {
        "ENGINE": "django.contrib.gis.db.backends.postgis",
        "NAME": name,
        "USER": DB_USERNAME,
        "PASSWORD": DB_PASSWORD,
        "HOST": POSTGRESQL_SERVICE_HOST,
        "PORT": POSTGRESQL_SERVICE_PORT,
    }

DATABASES["default"] = DATABASES[DEFAULT_DB]

# CORS settings for development
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True

CORS_ORIGIN_WHITELIST = [
    "http://localhost:3000",
    "https://customspace.global.anz.com",
]

CSRF_TRUSTED_ORIGINS = [
    "http://localhost:3000",
]

# Add debug toolbar for development
INSTALLED_APPS += [
    'debug_toolbar',
    'django_silk',
]

MIDDLEWARE += [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    'silk.middleware.SilkyMiddleware',
]

# Debug toolbar configuration
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

# Disable LDAP authentication in development
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
]

# Development logging
LOGGING['root']['level'] = 'DEBUG'

# Disable anonymization in development
ANONYMIZE_VALUES = False

# Development-specific settings
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Cache configuration for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Static files configuration for development
STATICFILES_DIRS = [
    BASE_DIR / "static",
]
