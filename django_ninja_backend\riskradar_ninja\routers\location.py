from ninja import Router
from typing import List, Optional
from django.shortcuts import get_object_or_404
from django.http import Http404

# Import models from the original server (we'll copy these later)
try:
    from riskradar.models.location.location import Location
except ImportError:
    # Placeholder for when models are copied
    Location = None

from ..schemas import RiskLocationSchema, RiskLocationListSchema, RiskLocationCreateSchema

router = Router()

@router.get("/", response=RiskLocationListSchema)
def list_risk_locations(request, limit: int = 100, offset: int = 0):
    """List risk locations with pagination"""
    if Location is None:
        return {"count": 0, "results": []}
    
    locations = Location.objects.all()[offset:offset + limit]
    count = Location.objects.count()
    
    return {
        "count": count,
        "results": [RiskLocationSchema.from_orm(location) for location in locations]
    }

@router.get("/{location_id}", response=RiskLocationSchema)
def get_risk_location(request, location_id: int):
    """Get a specific risk location by ID"""
    if Location is None:
        raise Http404("Location model not available")
    
    location = get_object_or_404(Location, location_id=location_id)
    return RiskLocationSchema.from_orm(location)

@router.post("/", response=RiskLocationSchema)
def create_risk_location(request, payload: RiskLocationCreateSchema):
    """Create a new risk location"""
    if Location is None:
        raise Http404("Location model not available")
    
    location = Location.objects.create(**payload.dict())
    return RiskLocationSchema.from_orm(location)

@router.put("/{location_id}", response=RiskLocationSchema)
def update_risk_location(request, location_id: int, payload: RiskLocationCreateSchema):
    """Update an existing risk location"""
    if Location is None:
        raise Http404("Location model not available")
    
    location = get_object_or_404(Location, location_id=location_id)
    for attr, value in payload.dict().items():
        setattr(location, attr, value)
    location.save()
    return RiskLocationSchema.from_orm(location)

@router.delete("/{location_id}")
def delete_risk_location(request, location_id: int):
    """Delete a risk location"""
    if Location is None:
        raise Http404("Location model not available")
    
    location = get_object_or_404(Location, location_id=location_id)
    location.delete()
    return {"success": True}
