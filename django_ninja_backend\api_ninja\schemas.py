from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

class HealthSchema(BaseModel):
    status: str
    message: str

class BaseResponseSchema(BaseModel):
    """Base response schema with common fields"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class AddressSchema(BaseResponseSchema):
    """Address schema for API responses"""
    address_id: int
    full_address: Optional[str] = None
    lat: Optional[float] = None
    lng: Optional[float] = None
    
    class Config:
        from_attributes = True

class SaleSchema(BaseResponseSchema):
    """Sale schema for API responses"""
    sale_id: int
    address: Optional[AddressSchema] = None
    sale_date: Optional[datetime] = None
    gross_sales_price: Optional[float] = None
    improvements_value: Optional[float] = None
    total_ha: Optional[float] = None
    vendor: Optional[str] = None
    purchaser: Optional[str] = None
    
    class Config:
        from_attributes = True

class ValuationSchema(BaseResponseSchema):
    """Valuation schema for API responses"""
    valuation_reference: Optional[str] = None
    valuation_name: Optional[str] = None
    address: Optional[AddressSchema] = None
    valuation_date: Optional[datetime] = None
    total_value: Optional[Decimal] = None
    
    class Config:
        from_attributes = True

class CustomerSchema(BaseResponseSchema):
    """Customer schema for API responses"""
    customer_sk: int
    customer_number: Optional[str] = None
    full_name: Optional[str] = None
    customer_type: Optional[str] = None
    customer_set_code: Optional[str] = None
    
    class Config:
        from_attributes = True

# Request schemas
class AddressCreateSchema(BaseModel):
    """Schema for creating addresses"""
    full_address: str
    lat: Optional[float] = None
    lng: Optional[float] = None

class SaleCreateSchema(BaseModel):
    """Schema for creating sales"""
    address_id: int
    sale_date: Optional[datetime] = None
    gross_sales_price: Optional[float] = None
    improvements_value: Optional[float] = None
    total_ha: Optional[float] = None
    vendor: Optional[str] = None
    purchaser: Optional[str] = None

class ValuationCreateSchema(BaseModel):
    """Schema for creating valuations"""
    valuation_reference: Optional[str] = None
    valuation_name: Optional[str] = None
    address_id: int
    valuation_date: Optional[datetime] = None
    total_value: Optional[Decimal] = None

# List response schemas
class AddressListSchema(BaseModel):
    """Schema for address list responses"""
    count: int
    results: List[AddressSchema]

class SaleListSchema(BaseModel):
    """Schema for sale list responses"""
    count: int
    results: List[SaleSchema]

class ValuationListSchema(BaseModel):
    """Schema for valuation list responses"""
    count: int
    results: List[ValuationSchema]

class CustomerListSchema(BaseModel):
    """Schema for customer list responses"""
    count: int
    results: List[CustomerSchema]
