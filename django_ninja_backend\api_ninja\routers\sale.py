from ninja import Router
from typing import List, Optional
from django.shortcuts import get_object_or_404
from django.http import Http404

# Import models from the original server (we'll copy these later)
try:
    from api.models.sale import Sale
    from api.models.common.address import Address
except ImportError:
    # Placeholder for when models are copied
    Sale = None
    Address = None

from ..schemas import SaleSchema, SaleListSchema, SaleCreateSchema

router = Router()

@router.get("/", response=SaleListSchema)
def list_sales(request, limit: int = 100, offset: int = 0):
    """List sales with pagination"""
    if Sale is None:
        return {"count": 0, "results": []}
    
    sales = Sale.objects.select_related('address').all()[offset:offset + limit]
    count = Sale.objects.count()
    
    return {
        "count": count,
        "results": [SaleSchema.from_orm(sale) for sale in sales]
    }

@router.get("/{sale_id}", response=SaleSchema)
def get_sale(request, sale_id: int):
    """Get a specific sale by ID"""
    if Sale is None:
        raise Http404("Sale model not available")
    
    sale = get_object_or_404(Sale.objects.select_related('address'), sale_id=sale_id)
    return SaleSchema.from_orm(sale)

@router.post("/", response=SaleSchema)
def create_sale(request, payload: SaleCreateSchema):
    """Create a new sale"""
    if Sale is None:
        raise Http404("Sale model not available")
    
    # Get the address
    address = get_object_or_404(Address, address_id=payload.address_id)
    
    sale_data = payload.dict()
    sale_data['address'] = address
    del sale_data['address_id']
    
    sale = Sale.objects.create(**sale_data)
    return SaleSchema.from_orm(sale)

@router.put("/{sale_id}", response=SaleSchema)
def update_sale(request, sale_id: int, payload: SaleCreateSchema):
    """Update an existing sale"""
    if Sale is None:
        raise Http404("Sale model not available")
    
    sale = get_object_or_404(Sale, sale_id=sale_id)
    
    # Update address if provided
    if payload.address_id:
        address = get_object_or_404(Address, address_id=payload.address_id)
        sale.address = address
    
    # Update other fields
    for attr, value in payload.dict(exclude={'address_id'}).items():
        if value is not None:
            setattr(sale, attr, value)
    
    sale.save()
    return SaleSchema.from_orm(sale)

@router.delete("/{sale_id}")
def delete_sale(request, sale_id: int):
    """Delete a sale"""
    if Sale is None:
        raise Http404("Sale model not available")
    
    sale = get_object_or_404(Sale, sale_id=sale_id)
    sale.delete()
    return {"success": True}

@router.get("/by-address/{address_id}", response=SaleListSchema)
def get_sales_by_address(request, address_id: int, limit: int = 100):
    """Get sales for a specific address"""
    if Sale is None:
        return {"count": 0, "results": []}
    
    sales = Sale.objects.filter(address__address_id=address_id)[:limit]
    
    return {
        "count": sales.count(),
        "results": [SaleSchema.from_orm(sale) for sale in sales]
    }
