from ninja import Router
from typing import List, Optional
from django.shortcuts import get_object_or_404
from django.http import Http404

# Import models from the original server (we'll copy these later)
# For now, we'll create placeholder imports
try:
    from api.models.common.address import Address
except ImportError:
    # Placeholder for when models are copied
    Address = None

from ..schemas import AddressSchema, AddressListSchema, AddressCreateSchema

router = Router()

@router.get("/", response=AddressListSchema)
def list_addresses(request, limit: int = 100, offset: int = 0):
    """List addresses with pagination"""
    if Address is None:
        return {"count": 0, "results": []}
    
    addresses = Address.objects.all()[offset:offset + limit]
    count = Address.objects.count()
    
    return {
        "count": count,
        "results": [AddressSchema.from_orm(addr) for addr in addresses]
    }

@router.get("/{address_id}", response=AddressSchema)
def get_address(request, address_id: int):
    """Get a specific address by ID"""
    if Address is None:
        raise Http404("Address model not available")
    
    address = get_object_or_404(Address, address_id=address_id)
    return AddressSchema.from_orm(address)

@router.post("/", response=AddressSchema)
def create_address(request, payload: AddressCreateSchema):
    """Create a new address"""
    if Address is None:
        raise Http404("Address model not available")
    
    address = Address.objects.create(**payload.dict())
    return AddressSchema.from_orm(address)

@router.put("/{address_id}", response=AddressSchema)
def update_address(request, address_id: int, payload: AddressCreateSchema):
    """Update an existing address"""
    if Address is None:
        raise Http404("Address model not available")
    
    address = get_object_or_404(Address, address_id=address_id)
    for attr, value in payload.dict().items():
        setattr(address, attr, value)
    address.save()
    return AddressSchema.from_orm(address)

@router.delete("/{address_id}")
def delete_address(request, address_id: int):
    """Delete an address"""
    if Address is None:
        raise Http404("Address model not available")
    
    address = get_object_or_404(Address, address_id=address_id)
    address.delete()
    return {"success": True}

@router.get("/search/", response=AddressListSchema)
def search_addresses(request, q: str, limit: int = 100):
    """Search addresses by text"""
    if Address is None:
        return {"count": 0, "results": []}
    
    addresses = Address.objects.filter(
        full_address__icontains=q
    )[:limit]
    
    return {
        "count": addresses.count(),
        "results": [AddressSchema.from_orm(addr) for addr in addresses]
    }
