from .base import *
import os
import ldap
from django_auth_ldap.config import LDAPSearch, GroupOfNamesType

# Production-specific settings
DEBUG = False

# Security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Database configuration for production
DATABASES = {
    "default": {
        "ENGINE": "django.contrib.gis.db.backends.postgis",
        "NAME": "agrigis",
        "USER": os.environ.get('DB_USERNAME'),
        "PASSWORD": os.environ.get('DB_PASSWORD'),
        "HOST": os.environ.get('POSTGRESQL_SERVICE_HOST'),
        "PORT": os.environ.get('POSTGRESQL_SERVICE_PORT'),
        "CONN_MAX_AGE": 240,
    }
}

# CORS settings for production
CORS_ORIGIN_ALLOW_ALL = False
CORS_ORIGIN_WHITELIST = [
    'https://customspace.global.anz.com/',
]

# LDAP Authentication configuration
LDAP_BIND_DN = os.environ.get('LDAP_BIND_DN')
LDAP_BIND_PASSWORD = os.environ.get('LDAP_BIND_PASSWORD')
LDAP_SERVER_URI = os.environ.get('LDAP_SERVER_URI', 'ldaps://global-ldap.lb.apps.anz:636')

if not (LDAP_BIND_DN and LDAP_BIND_PASSWORD):
    raise Exception(
        "ESGIS requires LDAP for authenticating users but LDAP_BIND_DN / LDAP_BIND_PASSWORD are not set."
    )

AUTHENTICATION_BACKENDS = [
    "axes.backends.AxesStandaloneBackend",
    "django_auth_ldap.backend.LDAPBackend",
    "django.contrib.auth.backends.ModelBackend",
]

AUTH_LDAP_SERVER_URI = LDAP_SERVER_URI
AUTH_LDAP_CONNECTION_OPTIONS = {
    ldap.OPT_X_TLS_CACERTFILE: "/etc/pki/tls/certs/ca-bundle.crt",
    ldap.OPT_X_TLS_NEWCTX: 0,
}

AUTH_LDAP_BIND_DN = LDAP_BIND_DN
AUTH_LDAP_BIND_PASSWORD = LDAP_BIND_PASSWORD

AUTH_LDAP_USER_SEARCH = LDAPSearch(
    "ou=people,dc=global,dc=anz",
    ldap.SCOPE_SUBTREE,
    "(sAMAccountName=%(user)s)",
)

AUTH_LDAP_GROUP_SEARCH = LDAPSearch(
    "ou=groups,dc=global,dc=anz",
    ldap.SCOPE_SUBTREE,
    "(objectClass=group)",
)

AUTH_LDAP_GROUP_TYPE = GroupOfNamesType(name_attr="cn")

AUTH_LDAP_USER_ATTR_MAP = {
    "first_name": "givenName",
    "last_name": "sn",
    "email": "mail",
}

AUTH_LDAP_FIND_GROUP_PERMS = True
AUTH_LDAP_CACHE_TIMEOUT = int(os.environ.get('LDAP_CACHE_TIMEOUT_SECONDS', 86400))

# Required LDAP groups
LDAP_REQUIRED_GROUPS = os.environ.get('LDAP_REQUIRED_GROUPS', '').split(',')

# Logging configuration for production
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django_auth_ldap': {
            'level': 'INFO',
            'handlers': ['console'],
        },
    },
}

# Cache configuration for production
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# Enable anonymization in production
ANONYMIZE_VALUES = True

# Base URL for production
BASE_URL = os.environ.get(
    "BASE_URL", "https://agrigis-ca-insights-prod.caas.nz.service.anz/"
)

# Static files configuration for production
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Email configuration for production
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.anz.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')

# Security headers
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
