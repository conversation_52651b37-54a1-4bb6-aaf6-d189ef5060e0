from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

class BaseResponseSchema(BaseModel):
    """Base response schema with common fields"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class RiskLocationSchema(BaseResponseSchema):
    """Risk location schema for API responses"""
    location_id: int
    name: Optional[str] = None
    lat: Optional[float] = None
    lng: Optional[float] = None
    risk_level: Optional[str] = None
    
    class Config:
        from_attributes = True

class RiskAssessmentSchema(BaseResponseSchema):
    """Risk assessment schema for API responses"""
    assessment_id: int
    location: Optional[RiskLocationSchema] = None
    assessment_date: Optional[datetime] = None
    risk_score: Optional[float] = None
    risk_category: Optional[str] = None
    
    class Config:
        from_attributes = True

# Request schemas
class RiskLocationCreateSchema(BaseModel):
    """Schema for creating risk locations"""
    name: str
    lat: Optional[float] = None
    lng: Optional[float] = None
    risk_level: Optional[str] = None

class RiskAssessmentCreateSchema(BaseModel):
    """Schema for creating risk assessments"""
    location_id: int
    assessment_date: Optional[datetime] = None
    risk_score: Optional[float] = None
    risk_category: Optional[str] = None

# List response schemas
class RiskLocationListSchema(BaseModel):
    """Schema for risk location list responses"""
    count: int
    results: List[RiskLocationSchema]

class RiskAssessmentListSchema(BaseModel):
    """Schema for risk assessment list responses"""
    count: int
    results: List[RiskAssessmentSchema]
