# ESGIS Django Ninja Backend

This is the Django Ninja conversion of the ESGIS backend, providing a modern, fast API using Django Ninja framework.

## Project Structure

```
django_ninja_backend/
├── manage.py                    # Django management script
├── pyproject.toml              # Poetry dependencies and configuration
├── .env.example                # Environment variables template
├── esgis_ninja/                # Main Django project
│   ├── settings/               # Settings modules
│   │   ├── base.py            # Base settings
│   │   ├── development.py     # Development settings
│   │   └── production.py      # Production settings
│   ├── urls.py                # Main URL configuration
│   ├── wsgi.py                # WSGI application
│   └── asgi.py                # ASGI application
├── api_ninja/                  # Core API app
│   ├── routers/               # API route handlers
│   ├── schemas.py             # Pydantic schemas
│   └── api.py                 # Main API router
├── riskradar_ninja/           # RiskRadar API app
├── green_ninja/               # Green finance API app
├── finance_ninja/             # Finance API app
├── ccra_ninja/                # CCRA API app
└── propertyflow_ninja/        # PropertyFlow API app
```

## Features

- **Django Ninja**: Fast, modern API framework with automatic OpenAPI documentation
- **Pydantic Schemas**: Type-safe request/response validation
- **Modular Architecture**: Separate apps for different business domains
- **LDAP Authentication**: Enterprise authentication support
- **PostGIS Support**: Geospatial data handling
- **Redis Caching**: Performance optimization
- **Comprehensive Logging**: Structured logging with structlog

## Setup

1. **Install Dependencies**
   ```bash
   cd django_ninja_backend
   poetry install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup**
   ```bash
   poetry run python manage.py migrate
   ```

4. **Run Development Server**
   ```bash
   poetry run python manage.py runserver
   ```

## API Documentation

Once the server is running, you can access:
- **API Documentation**: http://localhost:8000/api/docs/
- **Health Check**: http://localhost:8000/api/health

## Environment Variables

See `.env.example` for all available configuration options.

### Development Settings

For development, use:
```bash
export DJANGO_SETTINGS_MODULE=esgis_ninja.settings.development
```

### Production Settings

For production, use:
```bash
export DJANGO_SETTINGS_MODULE=esgis_ninja.settings.production
```

## API Endpoints

### Core API (`/api/`)
- `/api/health` - Health check
- `/api/addresses/` - Address management
- `/api/sales/` - Sales data
- `/api/valuations/` - Property valuations
- `/api/customers/` - Customer data

### RiskRadar API (`/api/riskradar/`)
- `/api/riskradar/locations/` - Risk locations
- `/api/riskradar/risk-assessments/` - Risk assessments

### Green Finance API (`/api/green/`)
- Coming soon...

### Finance API (`/api/finance/`)
- Coming soon...

## Development

### Adding New Endpoints

1. Create schemas in `{app}_ninja/schemas.py`
2. Create routers in `{app}_ninja/routers/`
3. Register routers in `{app}_ninja/api.py`

### Running Tests

```bash
poetry run python manage.py test
```

### Code Quality

```bash
# Format code
poetry run black .

# Run linting
poetry run flake8
```

## Migration from DRF

This project is a conversion from Django REST Framework to Django Ninja. The original models will be copied and adapted as needed.

## Deployment

The application supports both WSGI and ASGI deployment:

- **WSGI**: Use `esgis_ninja.wsgi:application`
- **ASGI**: Use `esgis_ninja.asgi:application`

## Contributing

1. Follow the existing code structure
2. Add appropriate tests for new features
3. Update documentation as needed
4. Use type hints and Pydantic schemas for all API endpoints
