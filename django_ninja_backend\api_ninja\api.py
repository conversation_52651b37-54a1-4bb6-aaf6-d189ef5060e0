from ninja import Router
from typing import List
from .schemas import HealthSchema

api = Router()

@api.get("/health", response=HealthSchema)
def health_check(request):
    """Health check endpoint"""
    return {"status": "ok", "message": "API is running"}

# Import and add other routers
from .routers.address import router as address_router
from .routers.sale import router as sale_router
from .routers.valuation import router as valuation_router
from .routers.customer import router as customer_router

api.add_router("/addresses", address_router)
api.add_router("/sales", sale_router)
api.add_router("/valuations", valuation_router)
api.add_router("/customers", customer_router)
