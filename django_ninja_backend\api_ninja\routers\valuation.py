from ninja import Router
from typing import List, Optional
from django.shortcuts import get_object_or_404
from django.http import Http404

# Import models from the original server (we'll copy these later)
try:
    from api.models.valuation import Valuation
    from api.models.common.address import Address
except ImportError:
    # Placeholder for when models are copied
    Valuation = None
    Address = None

from ..schemas import ValuationSchema, ValuationListSchema, ValuationCreateSchema

router = Router()

@router.get("/", response=ValuationListSchema)
def list_valuations(request, limit: int = 100, offset: int = 0):
    """List valuations with pagination"""
    if Valuation is None:
        return {"count": 0, "results": []}
    
    valuations = Valuation.objects.select_related('address').all()[offset:offset + limit]
    count = Valuation.objects.count()
    
    return {
        "count": count,
        "results": [ValuationSchema.from_orm(valuation) for valuation in valuations]
    }

@router.get("/{valuation_id}", response=ValuationSchema)
def get_valuation(request, valuation_id: int):
    """Get a specific valuation by ID"""
    if Valuation is None:
        raise Http404("Valuation model not available")
    
    valuation = get_object_or_404(Valuation.objects.select_related('address'), id=valuation_id)
    return ValuationSchema.from_orm(valuation)

@router.post("/", response=ValuationSchema)
def create_valuation(request, payload: ValuationCreateSchema):
    """Create a new valuation"""
    if Valuation is None:
        raise Http404("Valuation model not available")
    
    # Get the address
    address = get_object_or_404(Address, address_id=payload.address_id)
    
    valuation_data = payload.dict()
    valuation_data['address'] = address
    del valuation_data['address_id']
    
    valuation = Valuation.objects.create(**valuation_data)
    return ValuationSchema.from_orm(valuation)

@router.put("/{valuation_id}", response=ValuationSchema)
def update_valuation(request, valuation_id: int, payload: ValuationCreateSchema):
    """Update an existing valuation"""
    if Valuation is None:
        raise Http404("Valuation model not available")
    
    valuation = get_object_or_404(Valuation, id=valuation_id)
    
    # Update address if provided
    if payload.address_id:
        address = get_object_or_404(Address, address_id=payload.address_id)
        valuation.address = address
    
    # Update other fields
    for attr, value in payload.dict(exclude={'address_id'}).items():
        if value is not None:
            setattr(valuation, attr, value)
    
    valuation.save()
    return ValuationSchema.from_orm(valuation)

@router.delete("/{valuation_id}")
def delete_valuation(request, valuation_id: int):
    """Delete a valuation"""
    if Valuation is None:
        raise Http404("Valuation model not available")
    
    valuation = get_object_or_404(Valuation, id=valuation_id)
    valuation.delete()
    return {"success": True}

@router.get("/by-address/{address_id}", response=ValuationListSchema)
def get_valuations_by_address(request, address_id: int, limit: int = 100):
    """Get valuations for a specific address"""
    if Valuation is None:
        return {"count": 0, "results": []}
    
    valuations = Valuation.objects.filter(address__address_id=address_id)[:limit]
    
    return {
        "count": valuations.count(),
        "results": [ValuationSchema.from_orm(valuation) for valuation in valuations]
    }
