# Django settings
SECRET_KEY=your-secret-key-here
DEBUG=True
DJANGO_SETTINGS_MODULE=esgis_ninja.settings.development

# Database settings
DB_NAME=agrigis
DB_USERNAME=django
DB_PASSWORD=your-db-password
POSTGRESQL_SERVICE_HOST=localhost
POSTGRESQL_SERVICE_PORT=5432

# LDAP settings (for production)
LDAP_BIND_DN=your-ldap-bind-dn
LDAP_BIND_PASSWORD=your-ldap-password
LDAP_SERVER_URI=ldaps://global-ldap.lb.apps.anz:636
LDAP_REQUIRED_GROUPS=group1,group2
LDAP_CACHE_TIMEOUT_SECONDS=86400

# Redis settings
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Teradata settings
TERADATA_CONNECTION_STRING=your-teradata-connection

# S3 settings
S3_IIH_INBOUND_ACCESS_KEY_ID=your-s3-access-key
S3_IIH_INBOUND_SECRET_KEY=your-s3-secret-key
S3_IIH_INBOUND_BUCKET_NAME=your-s3-bucket
S3_IIH_INBOUND_HOST=your-s3-endpoint

# Application settings
BASE_URL=http://localhost:8000
ANONYMIZE_VALUES=False

# Email settings (for production)
EMAIL_HOST=smtp.anz.com
EMAIL_PORT=587
EMAIL_HOST_USER=your-email-user
EMAIL_HOST_PASSWORD=your-email-password
