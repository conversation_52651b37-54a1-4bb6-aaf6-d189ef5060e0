from ninja import Router
from typing import List, Optional
from django.shortcuts import get_object_or_404
from django.http import Http404

# Import models from the original server (we'll copy these later)
try:
    from api.models.remote.customers import Customer
except ImportError:
    # Placeholder for when models are copied
    Customer = None

from ..schemas import CustomerSchema, CustomerListSchema

router = Router()

@router.get("/", response=CustomerListSchema)
def list_customers(request, limit: int = 100, offset: int = 0):
    """List customers with pagination"""
    if Customer is None:
        return {"count": 0, "results": []}
    
    customers = Customer.objects.all()[offset:offset + limit]
    count = Customer.objects.count()
    
    return {
        "count": count,
        "results": [CustomerSchema.from_orm(customer) for customer in customers]
    }

@router.get("/{customer_sk}", response=CustomerSchema)
def get_customer(request, customer_sk: int):
    """Get a specific customer by SK"""
    if Customer is None:
        raise Http404("Customer model not available")
    
    customer = get_object_or_404(Customer, customer_sk=customer_sk)
    return CustomerSchema.from_orm(customer)

@router.get("/search/", response=CustomerListSchema)
def search_customers(request, q: str, limit: int = 100):
    """Search customers by name or number"""
    if Customer is None:
        return {"count": 0, "results": []}
    
    customers = Customer.objects.filter(
        full_name__icontains=q
    ) | Customer.objects.filter(
        customer_number__icontains=q
    )
    customers = customers[:limit]
    
    return {
        "count": customers.count(),
        "results": [CustomerSchema.from_orm(customer) for customer in customers]
    }
