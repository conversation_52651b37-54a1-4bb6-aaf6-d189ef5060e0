from ninja import Router
from typing import List, Optional
from django.shortcuts import get_object_or_404
from django.http import Http404

# Import models from the original server (we'll copy these later)
try:
    from riskradar.models.risk_assessment import RiskAssessment
    from riskradar.models.location.location import Location
except ImportError:
    # Placeholder for when models are copied
    RiskAssessment = None
    Location = None

from ..schemas import RiskAssessmentSchema, RiskAssessmentListSchema, RiskAssessmentCreateSchema

router = Router()

@router.get("/", response=RiskAssessmentListSchema)
def list_risk_assessments(request, limit: int = 100, offset: int = 0):
    """List risk assessments with pagination"""
    if RiskAssessment is None:
        return {"count": 0, "results": []}
    
    assessments = RiskAssessment.objects.select_related('location').all()[offset:offset + limit]
    count = RiskAssessment.objects.count()
    
    return {
        "count": count,
        "results": [RiskAssessmentSchema.from_orm(assessment) for assessment in assessments]
    }

@router.get("/{assessment_id}", response=RiskAssessmentSchema)
def get_risk_assessment(request, assessment_id: int):
    """Get a specific risk assessment by ID"""
    if RiskAssessment is None:
        raise Http404("RiskAssessment model not available")
    
    assessment = get_object_or_404(RiskAssessment.objects.select_related('location'), assessment_id=assessment_id)
    return RiskAssessmentSchema.from_orm(assessment)

@router.post("/", response=RiskAssessmentSchema)
def create_risk_assessment(request, payload: RiskAssessmentCreateSchema):
    """Create a new risk assessment"""
    if RiskAssessment is None:
        raise Http404("RiskAssessment model not available")
    
    # Get the location
    location = get_object_or_404(Location, location_id=payload.location_id)
    
    assessment_data = payload.dict()
    assessment_data['location'] = location
    del assessment_data['location_id']
    
    assessment = RiskAssessment.objects.create(**assessment_data)
    return RiskAssessmentSchema.from_orm(assessment)

@router.put("/{assessment_id}", response=RiskAssessmentSchema)
def update_risk_assessment(request, assessment_id: int, payload: RiskAssessmentCreateSchema):
    """Update an existing risk assessment"""
    if RiskAssessment is None:
        raise Http404("RiskAssessment model not available")
    
    assessment = get_object_or_404(RiskAssessment, assessment_id=assessment_id)
    
    # Update location if provided
    if payload.location_id:
        location = get_object_or_404(Location, location_id=payload.location_id)
        assessment.location = location
    
    # Update other fields
    for attr, value in payload.dict(exclude={'location_id'}).items():
        if value is not None:
            setattr(assessment, attr, value)
    
    assessment.save()
    return RiskAssessmentSchema.from_orm(assessment)

@router.delete("/{assessment_id}")
def delete_risk_assessment(request, assessment_id: int):
    """Delete a risk assessment"""
    if RiskAssessment is None:
        raise Http404("RiskAssessment model not available")
    
    assessment = get_object_or_404(RiskAssessment, assessment_id=assessment_id)
    assessment.delete()
    return {"success": True}
