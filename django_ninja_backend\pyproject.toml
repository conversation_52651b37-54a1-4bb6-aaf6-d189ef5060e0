[tool.black]
target-version = ['py311']
include = '\.pyi?$'

[tool.mypy]
plugins = ["mypy_django_plugin.main"]

[tool.django-stubs]
django_settings_module = "esgis_ninja.settings"

[tool.poetry]
name = "esgis-ninja"
version = "0.1.0"
description = "Django Ninja backend for ESGIS"
authors = []

[tool.poetry.dependencies]
python = "^3.11"
coverage = "^7.4.3"
django = "4.2.17"
django-ninja = "^1.1.0"
django-admin-tools = "0.9.3"
django-advanced-filters = "2.0.0"
django-cors-headers = "4.3.1"
django-debug-toolbar = "4.3.0"
django-environ = "0.11.2"
django-extensions = "3.2.3"
django-filter = "23.5"
django-ipware = "6.0.4"
django-auth-ldap = "4.6.0"
django-safedelete = "1.3.3"
django-simple-history = "3.5.0"
django-structlog = "7.1.0"
django-clone = "3.0.2"
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
faker = "23.3.0"
flake8 = "7.0.0"
flake8-formatter-junit-xml = "0.0.6"
gunicorn = "21.2.0"
inflect = "7.0.0"
junit-xml = "1.9"
markdown = "3.5.2"
markdown2 = "2.4.13"
mock = "5.1.0"
model-bakery = "1.17.0"
numpy = "1.26.4"
pandas = "2.2.1"
psycopg2-binary = "2.9.9"
python-dateutil = "2.8.2"
pytz = "2024.1"
redis = "5.0.2"
requests = "2.31.0"
simplekml = "1.3.6"
sqlalchemy = "1.4.44"
structlog = "24.1.0"
teradatasql = "********"
teradatasqlalchemy = "*********"
unittest-xml-reporting = "3.2.0"
urllib3 = "1.26.17"
whitenoise = "6.6.0"
openpyxl = "3.1.2"
xlsxwriter = "3.2.0"
django-deprecate-fields = "0.1.2"
tqdm = "4.66.2"
boto3 = "1.34.52"
pytest = "8.0.0"
pytest-django = "4.8.0"
allure-pytest = "2.13.2"
setuptools = "69.1.1"
geojson-rewind = "1.1.0"
beautifulsoup4 = "4.12.3"
hiredis = "^2.3.2"
django-rq = "^2.10.2"
rq = ">=1.14,<2.0.0"
rich = "^13.7.1"
h11 = "^0.14.0"
uvicorn = {extras = ["standard"], version = "^0.29.0"}
nested-multipart-parser = "^1.5.0"
django-axes = {extras = ["ipware"], version = "^7.0.1"}

[tool.poetry.group.dev.dependencies]
django-silk = "^5.0.4"
black = "^24.2.0"
ipython = "^8.24.0"
networkx = "^3.4.2"

[[tool.poetry.source]]
name = "custom-pypi"
url = "https://artifactory-staging.nz.service.anz/artifactory/api/pypi/pypi-pythonhosted-remote/simple"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
