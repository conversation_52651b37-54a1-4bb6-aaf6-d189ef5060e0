from ninja import Router
from typing import List
from .schemas import RiskLocationSchema

api = Router()

@api.get("/health")
def riskradar_health_check(request):
    """RiskRadar health check endpoint"""
    return {"status": "ok", "message": "RiskRadar API is running"}

# Import and add other routers
from .routers.location import router as location_router
from .routers.risk_assessment import router as risk_assessment_router

api.add_router("/locations", location_router)
api.add_router("/risk-assessments", risk_assessment_router)
